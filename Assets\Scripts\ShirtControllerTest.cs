using Mediapipe.Tasks.Vision.PoseLandmarker;
using UnityEngine;
using System.Collections.Concurrent;
using Mediapipe.Unity.Sample.PoseLandmarkDetection;

[RequireComponent(typeof(PoseLandmarkerRunner))]
public class ShirtControllerTest : MonoBehaviour
{
    [Header("Prefab & Camera")]
    [SerializeField] private GameObject shirtPrefab;
    [SerializeField] private Camera mainCamera;

    [<PERSON><PERSON>("Calibration Defaults")]
    [Tooltip("Y offset to align shirt on torso")]
    [SerializeField] private float yOffsetDefault = -54.6f;
    [Tooltip("Uniform scale multiplier")]
    [SerializeField] private float scaleMultiplierDefault = 3.41f;

    [Header("Manual Rotation")]
    [Tooltip("Fixed yaw rotation in degrees")]
    [SerializeField] private float fixedYaw = 180f;
    [Tooltip("Small pitch adjustment if needed")]
    [SerializeField] private float fixedPitch = 0f;
    [<PERSON>lt<PERSON>("Roll (usually 0)")]
    [SerializeField] private float fixedRoll = 0f;

    [Header("Smoothing (0=no,1=freeze)")]
    [Range(0, 1)][SerializeField] private float posSmooth = 0.2f;
    [Range(0, 1)][SerializeField] private float rotSmooth = 0.2f;
    [Range(0, 1)][SerializeField] private float scaleSmooth = 0.2f;

    [Header("Visibility Threshold")]
    [SerializeField] private float minShoulderDistance = 0.05f;

    [Header("Hand Tracking")]
    [SerializeField] private bool enableHandTracking = true;
    [SerializeField] private float handSmoothingFactor = 0.3f;
    [SerializeField] private float handRotationMultiplier = 1.0f;

    [Header("Body Rotation Tracking")]
    [SerializeField] private bool enableBodyRotation = true;
    [SerializeField] private float bodyRotationSmoothingFactor = 0.2f;
    [SerializeField] private float bodyRotationMultiplier = 1.0f;

    private const string LandmarkPath =
        "Main Canvas/Container Panel/Body/Annotatable Screen/" +
        "Annotation Layer/MultiPoseLandmarkListWithMaskAnnotation/" +
        "PoseLandmarkListWithMaskAnnotation(Clone)/" +
        "PoseLandmarkList Annotation/Point List Annotation";

    private PoseLandmarkerRunner _runner;
    private Transform _landmarksRoot;
    private GameObject _instance;
    private ConcurrentQueue<PoseLandmarkerResult> _queue = new();
    private ShirtManipulator _shirtManipulator;

    private Vector3 _smoothedPos;
    private Quaternion _smoothedRot;
    private float _smoothedScale;

    // landmark positions
    private Vector3 _ls, _rs, _lh, _rh;

    // Additional landmark positions for hands and body rotation
    private Vector3 _leftElbow, _rightElbow, _leftWrist, _rightWrist;

    // Smoothed hand and body rotation values
    private float _smoothedLeftHandPitch, _smoothedLeftHandRoll;
    private float _smoothedRightHandPitch, _smoothedRightHandRoll;
    private float _smoothedUpperBodyYaw, _smoothedUpperBodyPitch, _smoothedUpperBodyRoll;

    void Awake()
    {
        _runner = GetComponent<PoseLandmarkerRunner>();
        _runner.ShirtController = this;
        if (!mainCamera) mainCamera = Camera.main;
    }

    public void OnPoseDetected(PoseLandmarkerResult result)
    {
        _queue.Enqueue(result);
    }

    void Update()
    {
        // find landmark root once
        if (_landmarksRoot == null)
        {
            var go = GameObject.Find(LandmarkPath);
            if (go != null) _landmarksRoot = go.transform;
            else return;
        }

        while (_queue.TryDequeue(out var res))
            ProcessPose(res);
    }

    private void ProcessPose(PoseLandmarkerResult res)
    {
        if (_landmarksRoot.childCount < 25)
        {
            HideShirt();
            return;
        }

        // world positions - existing landmarks
        _ls = _landmarksRoot.GetChild(11).position;  // Left Shoulder
        _rs = _landmarksRoot.GetChild(12).position;  // Right Shoulder
        _lh = _landmarksRoot.GetChild(23).position;  // Left Hip
        _rh = _landmarksRoot.GetChild(24).position;  // Right Hip

        // Additional landmarks for hand and body tracking
        _leftElbow = _landmarksRoot.GetChild(13).position;   // Left Elbow
        _rightElbow = _landmarksRoot.GetChild(14).position;  // Right Elbow
        _leftWrist = _landmarksRoot.GetChild(15).position;   // Left Wrist
        _rightWrist = _landmarksRoot.GetChild(16).position;  // Right Wrist

        float shoulderDist = Vector3.Distance(_ls, _rs);
        if (shoulderDist < minShoulderDistance)
        {
            HideShirt();
            return;
        }

        // instantiate shirt
        if (_instance == null)
        {
            _instance = Instantiate(shirtPrefab);
            _shirtManipulator = _instance.GetComponent<ShirtManipulator>();
            _smoothedPos = _instance.transform.position;
            _smoothedRot = _instance.transform.rotation;
            _smoothedScale = _shirtManipulator != null ? _shirtManipulator.scaleMultiplier : 1f;
        }

        // compute center between shoulders and hips
        var topMid = (_ls + _rs) * 0.5f;
        var botMid = (_lh + _rh) * 0.5f;
        var rawCenter = (topMid + botMid) * 0.5f;

        // apply calibrated offsets
        Vector3 targetPos = rawCenter + Vector3.up * yOffsetDefault;

        // apply uniform scale
        float uniform = Mathf.Max(
            shoulderDist,
            Vector3.Distance(topMid, botMid),
            shoulderDist * 0.5f
        ) * scaleMultiplierDefault;
        float targetScale = uniform;

        // build fixed rotation
        float yaw = fixedYaw;
        // if mirrored camera feed (e.g. front camera), invert yaw
        if (_runner.ShirtController == this && mainCamera.transform.localEulerAngles.y > 90f)
            yaw = -fixedYaw;
        Quaternion targetRot = Quaternion.Euler(fixedPitch, yaw, fixedRoll);

        // smooth transitions
        _smoothedPos = Vector3.Lerp(_smoothedPos, targetPos, 1 - posSmooth);
        _smoothedRot = Quaternion.Slerp(_smoothedRot, targetRot, 1 - rotSmooth);
        _smoothedScale = Mathf.Lerp(_smoothedScale, targetScale, 1 - scaleSmooth);

        // apply position, rotation, and scale through ShirtManipulator
        _instance.transform.position = _smoothedPos;
        _instance.transform.rotation = _smoothedRot;

        // Use ShirtManipulator to handle scaling properly for all parts
        if (_shirtManipulator != null)
        {
            _shirtManipulator.scaleMultiplier = _smoothedScale;

            // Apply hand tracking if enabled
            if (enableHandTracking)
            {
                UpdateHandTracking();
            }

            // Apply body rotation tracking if enabled
            if (enableBodyRotation)
            {
                UpdateBodyRotationTracking();
            }
        }
        else
        {
            // Fallback to direct scaling if no ShirtManipulator
            _instance.transform.localScale = Vector3.one * _smoothedScale;
        }
        _instance.SetActive(true);
    }

    private void UpdateHandTracking()
    {
        if (_shirtManipulator == null) return;

        // Calculate left hand rotation based on arm pose
        Vector3 leftShoulderToElbow = _leftElbow - _ls;
        Vector3 leftElbowToWrist = _leftWrist - _leftElbow;

        // Calculate pitch (up/down rotation) for left hand
        float leftHandPitch = CalculateHandPitch(leftShoulderToElbow, leftElbowToWrist) * handRotationMultiplier;

        // Calculate roll (twist rotation) for left hand
        float leftHandRoll = CalculateHandRoll(leftShoulderToElbow, leftElbowToWrist) * handRotationMultiplier;

        // Calculate right hand rotation based on arm pose
        Vector3 rightShoulderToElbow = _rightElbow - _rs;
        Vector3 rightElbowToWrist = _rightWrist - _rightElbow;

        // Calculate pitch (up/down rotation) for right hand
        float rightHandPitch = CalculateHandPitch(rightShoulderToElbow, rightElbowToWrist) * handRotationMultiplier;

        // Calculate roll (twist rotation) for right hand
        float rightHandRoll = CalculateHandRoll(rightShoulderToElbow, rightElbowToWrist) * handRotationMultiplier;

        // Apply smoothing to hand rotations
        _smoothedLeftHandPitch = Mathf.Lerp(_smoothedLeftHandPitch, leftHandPitch, 1 - handSmoothingFactor);
        _smoothedLeftHandRoll = Mathf.Lerp(_smoothedLeftHandRoll, leftHandRoll, 1 - handSmoothingFactor);
        _smoothedRightHandPitch = Mathf.Lerp(_smoothedRightHandPitch, rightHandPitch, 1 - handSmoothingFactor);
        _smoothedRightHandRoll = Mathf.Lerp(_smoothedRightHandRoll, rightHandRoll, 1 - handSmoothingFactor);

        // Apply to ShirtManipulator
        _shirtManipulator.SetLeftHandRotation(_smoothedLeftHandPitch, _smoothedLeftHandRoll);
        _shirtManipulator.SetRightHandRotation(_smoothedRightHandPitch, _smoothedRightHandRoll);
    }

    private void UpdateBodyRotationTracking()
    {
        if (_shirtManipulator == null) return;

        // Calculate body rotation based on shoulder and hip positions
        Vector3 shoulderDirection = _rs - _ls;
        Vector3 hipDirection = _rh - _lh;
        Vector3 torsoDirection = ((_ls + _rs) * 0.5f) - ((_lh + _rh) * 0.5f);

        // Calculate yaw (left/right rotation) based on shoulder direction
        float bodyYaw = CalculateBodyYaw(shoulderDirection) * bodyRotationMultiplier;

        // Calculate pitch (forward/backward lean) based on torso direction
        float bodyPitch = CalculateBodyPitch(torsoDirection) * bodyRotationMultiplier;

        // Calculate roll (side lean) based on shoulder vs hip alignment
        float bodyRoll = CalculateBodyRoll(shoulderDirection, hipDirection) * bodyRotationMultiplier;

        // Apply smoothing to body rotations
        _smoothedUpperBodyYaw = Mathf.Lerp(_smoothedUpperBodyYaw, bodyYaw, 1 - bodyRotationSmoothingFactor);
        _smoothedUpperBodyPitch = Mathf.Lerp(_smoothedUpperBodyPitch, bodyPitch, 1 - bodyRotationSmoothingFactor);
        _smoothedUpperBodyRoll = Mathf.Lerp(_smoothedUpperBodyRoll, bodyRoll, 1 - bodyRotationSmoothingFactor);

        // Apply to ShirtManipulator
        _shirtManipulator.SetUpperBodyRotation(_smoothedUpperBodyYaw, _smoothedUpperBodyPitch, _smoothedUpperBodyRoll);
    }

    private void HideShirt()
    {
        if (_instance) _instance.SetActive(false);
    }

    void OnDisable()
    {
        if (_instance) Destroy(_instance);
    }

    // Helper methods for calculating rotations from MediaPipe landmarks
    private float CalculateHandPitch(Vector3 shoulderToElbow, Vector3 elbowToWrist)
    {
        // Calculate the angle between the upper arm and forearm in the vertical plane
        Vector3 upperArmProjected = Vector3.ProjectOnPlane(shoulderToElbow, Vector3.right);
        Vector3 forearmProjected = Vector3.ProjectOnPlane(elbowToWrist, Vector3.right);

        float angle = Vector3.SignedAngle(upperArmProjected, forearmProjected, Vector3.right);
        return Mathf.Clamp(angle * 0.5f, -90f, 90f); // Scale and clamp to reasonable range
    }

    private float CalculateHandRoll(Vector3 shoulderToElbow, Vector3 elbowToWrist)
    {
        // Calculate the twist rotation based on the cross product of arm segments
        Vector3 cross = Vector3.Cross(shoulderToElbow.normalized, elbowToWrist.normalized);
        float rollAngle = Vector3.Dot(cross, Vector3.forward) * 45f; // Scale to reasonable range
        return Mathf.Clamp(rollAngle, -45f, 45f);
    }

    private float CalculateBodyYaw(Vector3 shoulderDirection)
    {
        // Calculate yaw rotation based on shoulder direction relative to camera
        float yawAngle = Mathf.Atan2(shoulderDirection.x, shoulderDirection.z) * Mathf.Rad2Deg;
        return Mathf.Clamp(yawAngle, -180f, 180f);
    }

    private float CalculateBodyPitch(Vector3 torsoDirection)
    {
        // Calculate pitch (forward/backward lean) based on torso direction
        float pitchAngle = Mathf.Atan2(torsoDirection.z, torsoDirection.y) * Mathf.Rad2Deg;
        return Mathf.Clamp(pitchAngle * 0.5f, -30f, 30f); // Scale and clamp to reasonable range
    }

    private float CalculateBodyRoll(Vector3 shoulderDirection, Vector3 hipDirection)
    {
        // Calculate roll (side lean) based on the difference between shoulder and hip angles
        float shoulderAngle = Mathf.Atan2(shoulderDirection.y, shoulderDirection.x) * Mathf.Rad2Deg;
        float hipAngle = Mathf.Atan2(hipDirection.y, hipDirection.x) * Mathf.Rad2Deg;
        float rollAngle = shoulderAngle - hipAngle;
        return Mathf.Clamp(rollAngle * 0.3f, -45f, 45f); // Scale and clamp to reasonable range
    }
}
