using Mediapipe.Tasks.Vision.PoseLandmarker;
using UnityEngine;
using System.Collections.Concurrent;
using Mediapipe.Unity.Sample.PoseLandmarkDetection;

[RequireComponent(typeof(PoseLandmarkerRunner))]
public class ShirtControllerTest : MonoBehaviour
{
    [Header("Prefab & Camera")]
    [SerializeField] private GameObject shirtPrefab;
    [SerializeField] private Camera mainCamera;

    [<PERSON><PERSON>("Calibration Defaults")]
    [Tooltip("Y offset to align shirt on torso")]
    [SerializeField] private float yOffsetDefault = -54.6f;
    [Tooltip("Uniform scale multiplier")]
    [SerializeField] private float scaleMultiplierDefault = 3.41f;

    [Header("Manual Rotation")]
    [Tooltip("Fixed yaw rotation in degrees")]
    [SerializeField] private float fixedYaw = 180f;
    [Tooltip("Small pitch adjustment if needed")]
    [SerializeField] private float fixedPitch = 0f;
    [<PERSON>lt<PERSON>("Roll (usually 0)")]
    [SerializeField] private float fixedRoll = 0f;

    [Header("Smoothing (0=no,1=freeze)")]
    [Range(0, 1)][SerializeField] private float posSmooth = 0.2f;
    [Range(0, 1)][SerializeField] private float rotSmooth = 0.2f;
    [Range(0, 1)][SerializeField] private float scaleSmooth = 0.2f;

    [Header("Visibility Threshold")]
    [SerializeField] private float minShoulderDistance = 0.05f;

    private const string LandmarkPath =
        "Main Canvas/Container Panel/Body/Annotatable Screen/" +
        "Annotation Layer/MultiPoseLandmarkListWithMaskAnnotation/" +
        "PoseLandmarkListWithMaskAnnotation(Clone)/" +
        "PoseLandmarkList Annotation/Point List Annotation";

    private PoseLandmarkerRunner _runner;
    private Transform _landmarksRoot;
    private GameObject _instance;
    private ConcurrentQueue<PoseLandmarkerResult> _queue = new();
    private ShirtManipulator _shirtManipulator;

    private Vector3 _smoothedPos;
    private Quaternion _smoothedRot;
    private float _smoothedScale;

    // landmark positions
    private Vector3 _ls, _rs, _lh, _rh;

    void Awake()
    {
        _runner = GetComponent<PoseLandmarkerRunner>();
        _runner.ShirtController = this;
        if (!mainCamera) mainCamera = Camera.main;
    }

    public void OnPoseDetected(PoseLandmarkerResult result)
    {
        _queue.Enqueue(result);
    }

    void Update()
    {
        // find landmark root once
        if (_landmarksRoot == null)
        {
            var go = GameObject.Find(LandmarkPath);
            if (go != null) _landmarksRoot = go.transform;
            else return;
        }

        while (_queue.TryDequeue(out var res))
            ProcessPose(res);
    }

    private void ProcessPose(PoseLandmarkerResult res)
    {
        if (_landmarksRoot.childCount < 25)
        {
            HideShirt();
            return;
        }

        // world positions
        _ls = _landmarksRoot.GetChild(11).position;
        _rs = _landmarksRoot.GetChild(12).position;
        _lh = _landmarksRoot.GetChild(23).position;
        _rh = _landmarksRoot.GetChild(24).position;

        float shoulderDist = Vector3.Distance(_ls, _rs);
        if (shoulderDist < minShoulderDistance)
        {
            HideShirt();
            return;
        }

        // instantiate shirt
        if (_instance == null)
        {
            _instance = Instantiate(shirtPrefab);
            _shirtManipulator = _instance.GetComponent<ShirtManipulator>();
            _smoothedPos = _instance.transform.position;
            _smoothedRot = _instance.transform.rotation;
            _smoothedScale = _shirtManipulator != null ? _shirtManipulator.scaleMultiplier : 1f;
        }

        // compute center between shoulders and hips
        var topMid = (_ls + _rs) * 0.5f;
        var botMid = (_lh + _rh) * 0.5f;
        var rawCenter = (topMid + botMid) * 0.5f;

        // apply calibrated offsets
        Vector3 targetPos = rawCenter + Vector3.up * yOffsetDefault;

        // apply uniform scale
        float uniform = Mathf.Max(
            shoulderDist,
            Vector3.Distance(topMid, botMid),
            shoulderDist * 0.5f
        ) * scaleMultiplierDefault;
        float targetScale = uniform;

        // build fixed rotation
        float yaw = fixedYaw;
        // if mirrored camera feed (e.g. front camera), invert yaw
        if (_runner.ShirtController == this && mainCamera.transform.localEulerAngles.y > 90f)
            yaw = -fixedYaw;
        Quaternion targetRot = Quaternion.Euler(fixedPitch, yaw, fixedRoll);

        // smooth transitions
        _smoothedPos = Vector3.Lerp(_smoothedPos, targetPos, 1 - posSmooth);
        _smoothedRot = Quaternion.Slerp(_smoothedRot, targetRot, 1 - rotSmooth);
        _smoothedScale = Mathf.Lerp(_smoothedScale, targetScale, 1 - scaleSmooth);

        // apply position, rotation, and scale through ShirtManipulator
        _instance.transform.position = _smoothedPos;
        _instance.transform.rotation = _smoothedRot;

        // Use ShirtManipulator to handle scaling properly for all parts
        if (_shirtManipulator != null)
        {
            _shirtManipulator.scaleMultiplier = _smoothedScale;
        }
        else
        {
            // Fallback to direct scaling if no ShirtManipulator
            _instance.transform.localScale = Vector3.one * _smoothedScale;
        }
        _instance.SetActive(true);
    }

    private void HideShirt()
    {
        if (_instance) _instance.SetActive(false);
    }

    void OnDisable()
    {
        if (_instance) Destroy(_instance);
    }
}
