using UnityEngine;
using Mediapipe.Tasks.Vision.PoseLandmarker;

/// <summary>
/// Dedicated component for tracking hand movements from MediaPipe pose landmarks
/// </summary>
public class MediaPipeHandTracker : MonoBehaviour
{
    [Header("Hand Tracking Settings")]
    [SerializeField] private bool enableHandTracking = true;
    [SerializeField] private float handSmoothingFactor = 0.3f;
    [SerializeField] private float handRotationMultiplier = 1.0f;
    [SerializeField] private bool debugMode = false;

    [Header("Landmark Indices")]
    [SerializeField] private int leftShoulderIndex = 11;
    [SerializeField] private int rightShoulderIndex = 12;
    [SerializeField] private int leftElbowIndex = 13;
    [SerializeField] private int rightElbowIndex = 14;
    [SerializeField] private int leftWristIndex = 15;
    [SerializeField] private int rightWristIndex = 16;

    // Events for hand rotation updates
    public System.Action<float, float> OnLeftHandRotationUpdated;
    public System.Action<float, float> OnRightHandRotationUpdated;

    // Current landmark positions
    private Vector3 _leftShoulder, _rightShoulder;
    private Vector3 _leftElbow, _rightElbow;
    private Vector3 _leftWrist, _rightWrist;

    // Smoothed rotation values
    private float _smoothedLeftHandPitch, _smoothedLeftHandRoll;
    private float _smoothedRightHandPitch, _smoothedRightHandRoll;

    // Target rotation values
    private float _targetLeftHandPitch, _targetLeftHandRoll;
    private float _targetRightHandPitch, _targetRightHandRoll;

    public bool IsHandTrackingEnabled => enableHandTracking;

    /// <summary>
    /// Update hand tracking with new landmark positions
    /// </summary>
    /// <param name="landmarksRoot">Transform containing all pose landmarks as children</param>
    public void UpdateHandTracking(Transform landmarksRoot)
    {
        if (!enableHandTracking || landmarksRoot == null) return;

        if (landmarksRoot.childCount < 25)
        {
            if (debugMode) Debug.LogWarning("MediaPipeHandTracker: Not enough landmarks for hand tracking");
            return;
        }

        // Extract landmark positions
        ExtractLandmarkPositions(landmarksRoot);

        // Calculate hand rotations
        CalculateHandRotations();

        // Apply smoothing
        ApplySmoothing();

        // Trigger events
        OnLeftHandRotationUpdated?.Invoke(_smoothedLeftHandPitch, _smoothedLeftHandRoll);
        OnRightHandRotationUpdated?.Invoke(_smoothedRightHandPitch, _smoothedRightHandRoll);

        if (debugMode)
        {
            Debug.Log($"Left Hand: Pitch={_smoothedLeftHandPitch:F2}, Roll={_smoothedLeftHandRoll:F2}");
            Debug.Log($"Right Hand: Pitch={_smoothedRightHandPitch:F2}, Roll={_smoothedRightHandRoll:F2}");
        }
    }

    private void ExtractLandmarkPositions(Transform landmarksRoot)
    {
        _leftShoulder = landmarksRoot.GetChild(leftShoulderIndex).position;
        _rightShoulder = landmarksRoot.GetChild(rightShoulderIndex).position;
        _leftElbow = landmarksRoot.GetChild(leftElbowIndex).position;
        _rightElbow = landmarksRoot.GetChild(rightElbowIndex).position;
        _leftWrist = landmarksRoot.GetChild(leftWristIndex).position;
        _rightWrist = landmarksRoot.GetChild(rightWristIndex).position;
    }

    private void CalculateHandRotations()
    {
        // Left hand calculations
        Vector3 leftShoulderToElbow = _leftElbow - _leftShoulder;
        Vector3 leftElbowToWrist = _leftWrist - _leftElbow;
        
        _targetLeftHandPitch = CalculateHandPitch(leftShoulderToElbow, leftElbowToWrist) * handRotationMultiplier;
        _targetLeftHandRoll = CalculateHandRoll(leftShoulderToElbow, leftElbowToWrist) * handRotationMultiplier;

        // Right hand calculations
        Vector3 rightShoulderToElbow = _rightElbow - _rightShoulder;
        Vector3 rightElbowToWrist = _rightWrist - _rightElbow;
        
        _targetRightHandPitch = CalculateHandPitch(rightShoulderToElbow, rightElbowToWrist) * handRotationMultiplier;
        _targetRightHandRoll = CalculateHandRoll(rightShoulderToElbow, rightElbowToWrist) * handRotationMultiplier;
    }

    private void ApplySmoothing()
    {
        float smoothingRate = 1 - handSmoothingFactor;
        
        _smoothedLeftHandPitch = Mathf.Lerp(_smoothedLeftHandPitch, _targetLeftHandPitch, smoothingRate);
        _smoothedLeftHandRoll = Mathf.Lerp(_smoothedLeftHandRoll, _targetLeftHandRoll, smoothingRate);
        _smoothedRightHandPitch = Mathf.Lerp(_smoothedRightHandPitch, _targetRightHandPitch, smoothingRate);
        _smoothedRightHandRoll = Mathf.Lerp(_smoothedRightHandRoll, _targetRightHandRoll, smoothingRate);
    }

    private float CalculateHandPitch(Vector3 shoulderToElbow, Vector3 elbowToWrist)
    {
        // Calculate the angle between the upper arm and forearm in the vertical plane
        Vector3 upperArmProjected = Vector3.ProjectOnPlane(shoulderToElbow, Vector3.right);
        Vector3 forearmProjected = Vector3.ProjectOnPlane(elbowToWrist, Vector3.right);
        
        float angle = Vector3.SignedAngle(upperArmProjected, forearmProjected, Vector3.right);
        return Mathf.Clamp(angle * 0.5f, -90f, 90f);
    }

    private float CalculateHandRoll(Vector3 shoulderToElbow, Vector3 elbowToWrist)
    {
        // Calculate the twist rotation based on the cross product of arm segments
        Vector3 cross = Vector3.Cross(shoulderToElbow.normalized, elbowToWrist.normalized);
        float rollAngle = Vector3.Dot(cross, Vector3.forward) * 45f;
        return Mathf.Clamp(rollAngle, -45f, 45f);
    }

    /// <summary>
    /// Get current smoothed hand rotation values
    /// </summary>
    public (float pitch, float roll) GetLeftHandRotation() => (_smoothedLeftHandPitch, _smoothedLeftHandRoll);
    public (float pitch, float roll) GetRightHandRotation() => (_smoothedRightHandPitch, _smoothedRightHandRoll);

    /// <summary>
    /// Reset all smoothed values to zero
    /// </summary>
    public void ResetHandRotations()
    {
        _smoothedLeftHandPitch = _smoothedLeftHandRoll = 0f;
        _smoothedRightHandPitch = _smoothedRightHandRoll = 0f;
        _targetLeftHandPitch = _targetLeftHandRoll = 0f;
        _targetRightHandPitch = _targetRightHandRoll = 0f;
    }

    void OnDrawGizmos()
    {
        if (!debugMode || !enableHandTracking) return;

        // Draw arm connections for debugging
        Gizmos.color = Color.green;
        if (_leftShoulder != Vector3.zero && _leftElbow != Vector3.zero && _leftWrist != Vector3.zero)
        {
            Gizmos.DrawLine(_leftShoulder, _leftElbow);
            Gizmos.DrawLine(_leftElbow, _leftWrist);
            Gizmos.DrawSphere(_leftWrist, 0.02f);
        }

        Gizmos.color = Color.blue;
        if (_rightShoulder != Vector3.zero && _rightElbow != Vector3.zero && _rightWrist != Vector3.zero)
        {
            Gizmos.DrawLine(_rightShoulder, _rightElbow);
            Gizmos.DrawLine(_rightElbow, _rightWrist);
            Gizmos.DrawSphere(_rightWrist, 0.02f);
        }
    }
}
