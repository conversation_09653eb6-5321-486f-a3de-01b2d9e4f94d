using UnityEngine;

public class ShirtManipulator : MonoBehaviour
{
    [Header("Body Transforms")]
    public Transform upperBody;
    public Transform lowerBody;
    public Transform bodyAnchor;

    [<PERSON>er("Hand Transforms")]
    public Transform leftHand;
    public Transform leftHandAnchor;
    public Transform leftHandShoulder;
    public Transform rightHand;
    public Transform rightHandAnchor;
    public Transform rightHandShoulder;

    [<PERSON>er("Body Rotation Settings")]
    [Range(-180, 180)] public float upperBodyYaw = 0f;
    [Range(-45, 45)] public float upperBodyRoll = 0f;
    [Range(-30, 30)] public float upperBodyPitch = 0f;
    [Range(-45, 45)] public float lowerBodyYaw = 0f;

    [Header("Hand Rotation Settings")]
    [Range(-90, 90)] public float leftHandPitch = 0f;
    [Range(-90, 90)] public float rightHandPitch = 0f;
    [Range(-45, 45)] public float leftHandRoll = 0f;
    [Range(-45, 45)] public float rightHandRoll = 0f;

    // Initial offsets and rotations
    private Vector3 upperBodyOffset;
    private Vector3 lowerBodyOffset;
    private Vector3 leftHandOffset;
    private Vector3 rightHandOffset;
    private Quaternion leftHandAnchorInitialRotation;
    private Quaternion rightHandAnchorInitialRotation;

    [Header("Scaler")]
    [Range(0f,100f)]public float scaleMultiplier = 1f;

    void Start()
    {
        // Disable Animator temporarily to ensure neutral pose
        leftHandShoulder = leftHand?.GetChild(0);
        rightHandShoulder = rightHand?.GetChild(0);
        Animator animator = GetComponent<Animator>();
        bool wasAnimatorEnabled = false;
        if (animator)
        {
            wasAnimatorEnabled = animator.enabled;
            animator.enabled = false;
        }

        StoreInitialOffsets();

        // Restore Animator state
        if (animator)
            animator.enabled = wasAnimatorEnabled;

        // Log offsets for debugging
        Debug.Log($"Left Hand Offset: {leftHandOffset}, Right Hand Offset: {rightHandOffset}");
    }

    void StoreInitialOffsets()
    {
        if (bodyAnchor && upperBody)
            upperBodyOffset = upperBody.position - bodyAnchor.position;
        
        if (bodyAnchor && lowerBody)
            lowerBodyOffset = lowerBody.position - bodyAnchor.position;
        
        if (leftHandAnchor && leftHand)
        {
            // Store the initial rotation of the anchor
            leftHandAnchorInitialRotation = leftHandAnchor.rotation;
            // Calculate offset in world space and then transform to anchor's local space
            leftHandOffset = leftHandAnchor.InverseTransformPoint(leftHand.position);
            if (leftHandOffset.magnitude < 0.01f)
                Debug.LogWarning("Left hand offset is near zero. Check if leftHand and leftHandAnchor are correctly positioned.");
        }
        
        if (rightHandAnchor && rightHand)
        {
            // Store the initial rotation of the anchor
            rightHandAnchorInitialRotation = rightHandAnchor.rotation;
            // Calculate offset in world space and then transform to anchor's local space
            rightHandOffset = rightHandAnchor.InverseTransformPoint(rightHand.position);
            if (rightHandOffset.magnitude < 0.01f)
                Debug.LogWarning("Right hand offset is near zero. Check if rightHand and rightHandAnchor are correctly positioned.");
        }
    }

    void LateUpdate()
    {
        UpdateBodyRotations();
        UpdateHandRotations();
        UpdateScale();
    }

    void UpdateBodyRotations()
    {
        if (!bodyAnchor || !upperBody || !lowerBody) return;

        // Upper body rotation around anchor
        Quaternion upperBodyRot = Quaternion.Euler(upperBodyPitch, upperBodyYaw, upperBodyRoll);
        upperBody.rotation = bodyAnchor.rotation * upperBodyRot;
        upperBody.position = bodyAnchor.position + upperBody.rotation * upperBodyOffset;

        // Lower body rotation around anchor (with reduced twist)
        Quaternion lowerBodyRot = Quaternion.Euler(0, lowerBodyYaw * 0.5f, 0);
        lowerBody.rotation = bodyAnchor.rotation * lowerBodyRot;
        lowerBody.position = bodyAnchor.position + lowerBody.rotation * lowerBodyOffset;
    }

    void UpdateScale()
    {
        if (!bodyAnchor) return;

        // Apply scale multiplier
        transform.localScale = Vector3.one * scaleMultiplier;
    }

    void UpdateHandRotations()
    {
        // Left hand
        if (leftHand && leftHandAnchor)
        {
            // Define the rotation for the hand (pitch around X, roll around Z)
            Quaternion handRot = Quaternion.Euler(leftHandPitch, 0, leftHandRoll);

            // Rotate the offset vector in the anchor's local space
            Vector3 rotatedOffset = leftHandAnchor.rotation * handRot * leftHandOffset;
            leftHand.position = leftHandAnchor.position + rotatedOffset;

            // Apply the combined rotation (anchor's initial rotation + hand's rotation)
            leftHand.rotation = leftHandAnchor.rotation * handRot;

            // Debug log to verify position and rotation
            Debug.Log($"Left Hand Position: {leftHand.position}, Rotation: {leftHand.rotation.eulerAngles}");
        }

        // Right hand
        if (rightHand && rightHandAnchor)
        {
            // Define the rotation for the hand (pitch around X, mirrored roll around Z)
            Quaternion handRot = Quaternion.Euler(rightHandPitch, 0, -rightHandRoll);

            // Rotate the offset vector in the anchor's local space
            Vector3 rotatedOffset = rightHandAnchor.rotation * handRot * rightHandOffset;
            rightHand.position = rightHandAnchor.position + rotatedOffset;

            // Apply the combined rotation (anchor's initial rotation + hand's rotation)
            rightHand.rotation = rightHandAnchor.rotation * handRot;

            // Debug log to verify position and rotation
            Debug.Log($"Right Hand Position: {rightHand.position}, Rotation: {rightHand.rotation.eulerAngles}");
        }
    }

    // Public methods for animation control
    public void SetUpperBodyRotation(float yaw, float pitch, float roll)
    {
        upperBodyYaw = Mathf.Clamp(yaw, -180, 180);
        upperBodyPitch = Mathf.Clamp(pitch, -30, 30);
        upperBodyRoll = Mathf.Clamp(roll, -45, 45);
    }

    public void SetLeftHandRotation(float pitch, float roll)
    {
        leftHandPitch = Mathf.Clamp(pitch, -90, 90);
        leftHandRoll = Mathf.Clamp(roll, -45, 45);
    }

    public void SetRightHandRotation(float pitch, float roll)
    {
        rightHandPitch = Mathf.Clamp(pitch, -90, 90);
        rightHandRoll = Mathf.Clamp(roll, -45, 45);
    }

    // Gizmos for visualizing anchors and hands
    void OnDrawGizmos()
    {
        if (leftHand && leftHandAnchor)
        {
            Gizmos.color = Color.red;
            Gizmos.DrawSphere(leftHandAnchor.position, 0.05f);
            Gizmos.DrawLine(leftHandAnchor.position, leftHand.position);
        }
        if (rightHand && rightHandAnchor)
        {
            Gizmos.color = Color.blue;
            Gizmos.DrawSphere(rightHandAnchor.position, 0.05f);
            Gizmos.DrawLine(rightHandAnchor.position, rightHand.position);
        }
    }
}