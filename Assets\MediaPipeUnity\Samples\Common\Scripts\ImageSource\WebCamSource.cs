// Copyright (c) 2021 homuler
//
// Use of this source code is governed by an MIT-style
// license that can be found in the LICENSE file or at
// https://opensource.org/licenses/MIT.

using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;

#if UNITY_ANDROID
using UnityEngine.Android;
#endif

namespace Mediapipe.Unity
{
    public class WebCamSource : ImageSource
    {
        private readonly int _preferableDefaultWidth = 1280;
        private const string _TAG = nameof(WebCamSource);

        private readonly ResolutionStruct[] _defaultAvailableResolutions;

        public WebCamSource(int preferableDefaultWidth, ResolutionStruct[] defaultAvailableResolutions)
        {
            _preferableDefaultWidth = preferableDefaultWidth;
            _defaultAvailableResolutions = defaultAvailableResolutions;
        }

        private static readonly object _PermissionLock = new object();
        private static bool _IsPermitted = false;
        private int currentIndex = 0;

        public WebCamTexture WebCamTexture => _webCamTexture;
        private WebCamTexture _webCamTexture;
        private WebCamTexture webCamTexture
        {
            get => _webCamTexture;
            set
            {
                if (_webCamTexture != null)
                {
                    _webCamTexture.Stop();
                }
                _webCamTexture = value;
            }
        }

        public override int textureWidth => !isPrepared ? 0 : webCamTexture.width;
        public override int textureHeight => !isPrepared ? 0 : webCamTexture.height;
        public override bool isVerticallyFlipped => isPrepared && webCamTexture.videoVerticallyMirrored;
        public override bool isFrontFacing => isPrepared && (webCamDevice is WebCamDevice valueOfWebCamDevice) && valueOfWebCamDevice.isFrontFacing;
        public override RotationAngle rotation => !isPrepared ? RotationAngle.Rotation0 : (RotationAngle)webCamTexture.videoRotationAngle;

        private WebCamDevice? _webCamDevice;
        private WebCamDevice? webCamDevice
        {
            get => _webCamDevice;
            set
            {
                if (_webCamDevice is WebCamDevice current && value is WebCamDevice next && next.name == current.name)
                {
                    return;
                }

                if (value == null)
                {
                    return;
                }

                _webCamDevice = value;
                resolution = GetDefaultResolution();
            }
        }

        public override string sourceName => (webCamDevice is WebCamDevice valueOfWebCamDevice) ? valueOfWebCamDevice.name : null;

        private WebCamDevice[] _availableSources;
        private WebCamDevice[] availableSources
        {
            get
            {
                if (_availableSources == null)
                {
                    _availableSources = WebCamTexture.devices;
                }
                return _availableSources;
            }
            set => _availableSources = value;
        }

        public override string[] sourceCandidateNames => availableSources?.Select(device => device.name).ToArray();

#pragma warning disable IDE0025
        public override ResolutionStruct[] availableResolutions
        {
            get
            {
#if (UNITY_ANDROID || UNITY_IOS) && !UNITY_EDITOR
                if (webCamDevice is WebCamDevice valueOfWebCamDevice)
                {
                    return valueOfWebCamDevice.availableResolutions.Select(resolution => new ResolutionStruct(resolution)).ToArray();
                }
#endif
                return webCamDevice == null ? null : _defaultAvailableResolutions;
            }
        }
#pragma warning restore IDE0025

        public override bool isPrepared => webCamTexture != null;
        public override bool isPlaying => webCamTexture != null && webCamTexture.isPlaying;

        private IEnumerator Initialize()
        {
            yield return GetPermission();

            if (!_IsPermitted)
            {
                yield break;
            }

            if (webCamDevice != null)
            {
                yield break;
            }

            availableSources = WebCamTexture.devices;

            if (availableSources != null && availableSources.Length > 0)
            {
                webCamDevice = availableSources[0];
            }
        }

        private IEnumerator GetPermission()
        {
            lock (_PermissionLock)
            {
                if (_IsPermitted)
                {
                    yield break;
                }

#if UNITY_ANDROID
                if (!Permission.HasUserAuthorizedPermission(Permission.Camera))
                {
                    Permission.RequestUserPermission(Permission.Camera);
                    // Wait a little longer to allow user to accept
                    float timer = 0f;
                    while (!Permission.HasUserAuthorizedPermission(Permission.Camera) && timer < 5f)
                    {
                        timer += Time.deltaTime;
                        yield return null;
                    }
                }
#elif UNITY_IOS
                if (!Application.HasUserAuthorization(UserAuthorization.WebCam))
                {
                    yield return Application.RequestUserAuthorization(UserAuthorization.WebCam);
                }
#endif

#if UNITY_ANDROID
                if (!Permission.HasUserAuthorizedPermission(Permission.Camera))
                {
                    Debug.LogWarning("Not permitted to use Camera");
                    yield break;
                }
#elif UNITY_IOS
                if (!Application.HasUserAuthorization(UserAuthorization.WebCam))
                {
                    Debug.LogWarning("Not permitted to use WebCam");
                    yield break;
                }
#endif
                _IsPermitted = true;

                yield return new WaitForEndOfFrame();
            }
        }

        public override void SelectSource(int sourceId)
        {
            if (sourceId < 0 || sourceId >= availableSources.Length)
            {
                throw new ArgumentException($"Invalid source ID: {sourceId}");
            }

            webCamDevice = availableSources[sourceId];
        }

        public override IEnumerator Play()
        {
            yield return Initialize();
            if (!_IsPermitted)
            {
                throw new InvalidOperationException("Not permitted to access cameras");
            }

            InitializeWebCamTexture();
            webCamTexture.Play();
            yield return WaitForWebCamTexture();
        }

        public override IEnumerator Resume()
        {
            if (!isPrepared)
            {
                throw new InvalidOperationException("WebCamTexture is not prepared yet");
            }
            if (!webCamTexture.isPlaying)
            {
                webCamTexture.Play();
            }
            yield return WaitForWebCamTexture();
        }

        public override void Pause()
        {
            if (isPlaying)
            {
                webCamTexture.Pause();
            }
        }

        public override void Stop()
        {
            if (webCamTexture != null)
            {
                Debug.Log("Stopping webcam texture...");
                webCamTexture.Stop();
                webCamTexture = null;
            }
        }

        public override Texture GetCurrentTexture() => webCamTexture;

        private ResolutionStruct GetDefaultResolution()
        {
            var resolutions = availableResolutions;
            return resolutions == null || resolutions.Length == 0
                ? new ResolutionStruct()
                : resolutions.OrderBy(resolution => resolution, new ResolutionStructComparer(_preferableDefaultWidth)).First();
        }

        private void InitializeWebCamTexture()
        {
            Stop();
            if (webCamDevice is WebCamDevice valueOfWebCamDevice)
            {
                webCamTexture = new WebCamTexture(valueOfWebCamDevice.name, resolution.width, resolution.height, (int)resolution.frameRate);
                return;
            }
            throw new InvalidOperationException("Cannot initialize WebCamTexture because WebCamDevice is not selected");
        }

        private IEnumerator WaitForWebCamTexture()
        {
            const float timeout = 5f;
            float timer = 0f;

            Debug.Log("Waiting for WebCamTexture to start...");

            while (webCamTexture != null && webCamTexture.width <= 16 && timer < timeout)
            {
                timer += Time.deltaTime;
                yield return null;
            }

            if (webCamTexture == null || webCamTexture.width <= 16)
            {
                Debug.LogError("Failed to start webcam texture. Ensure Android permissions are granted.");
                throw new TimeoutException("WebCamTexture failed to start");
            }

            Debug.Log($"WebCamTexture started: {webCamTexture.width}x{webCamTexture.height}");
        }

        /// <summary>
        /// Toggle between front and back cameras (if available).
        /// Only toggles between first two cameras (index 0 and 1).
        /// Does NOT start playing camera automatically!
        /// </summary>
        public void ToggleBetweenFrontAndBackCamera()
        {
            if (availableSources == null || availableSources.Length == 0)
            {
                Debug.LogWarning("No cameras found");
                return;
            }

            // Find index of currently active camera
            if (!webCamDevice.HasValue)
            {
                Debug.LogWarning("No camera currently selected");
                return;
            }

            int currentIndex = Array.FindIndex(availableSources, cam => cam.name == webCamDevice.Value.name);
            if (currentIndex == -1)
            {
                Debug.LogWarning("Current camera not found");
                return;
            }

            // Find first front and first back cameras
            int frontIndex = Array.FindIndex(availableSources, cam => cam.isFrontFacing);
            int backIndex = Array.FindIndex(availableSources, cam => !cam.isFrontFacing);

            if (frontIndex == -1 || backIndex == -1)
            {
                Debug.LogWarning("Could not find both front and back cameras");
                return;
            }

            // Toggle: if current is front, switch to back; else switch to front
            int nextIndex = (currentIndex == frontIndex) ? backIndex : frontIndex;

            webCamDevice = availableSources[nextIndex];
            Debug.Log($"Switching camera to {webCamDevice.Value.name}");
            InitializeWebCamTexture(); // Prepare new camera texture
        }


        private class ResolutionStructComparer : IComparer<ResolutionStruct>
        {
            private readonly int _preferableDefaultWidth;

            public ResolutionStructComparer(int preferableDefaultWidth)
            {
                _preferableDefaultWidth = preferableDefaultWidth;
            }

            public int Compare(ResolutionStruct a, ResolutionStruct b)
            {
                var aDiff = Mathf.Abs(a.width - _preferableDefaultWidth);
                var bDiff = Mathf.Abs(b.width - _preferableDefaultWidth);
                if (aDiff != bDiff) return aDiff - bDiff;
                if (a.height != b.height) return a.height - b.height;
                return (int)(a.frameRate - b.frameRate);
            }
        }
    }
}
